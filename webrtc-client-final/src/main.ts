import { io, Socket } from "socket.io-client";

// --- DOM 元素获取 ---
const connectBtn = document.getElementById('connectBtn') as HTMLButtonElement;
const disconnectBtn = document.getElementById('disconnectBtn') as HTMLButtonElement;
const remoteVideo = document.getElementById('remoteVideo') as HTMLVideoElement;

// --- 全局变量 ---
const SERVER_URL = "http://localhost:8080";
let socket: Socket;
let pc: RTCPeerConnection | null = null;

// STUN 服务器配置
const pcConfig = {
  iceServers: [
    {
      urls: 'stun:stun.l.google.com:19302',
    },
  ],
};

// --- UI 控制函数 ---
const setUiControls = (isConnected: boolean) => {
  connectBtn.disabled = isConnected;
  disconnectBtn.disabled = !isConnected;
};

// --- WebRTC 核心函数 ---
const createPeerConnection = () => {
  pc = new RTCPeerConnection(pcConfig);

  // 【重要】在这里告诉 PeerConnection 我们只想接收视频
  pc.addTransceiver('video', { direction: 'recvonly' });

  // 当接收到对方的媒体流时，显示在 remoteVideo 元素上
  pc.ontrack = (event) => {
    console.log("[Track] 接收到媒体流");
    if (remoteVideo.srcObject !== event.streams[0]) {
      remoteVideo.srcObject = event.streams[0];
    }
  };

  pc.onconnectionstatechange = () => {
    console.log(`[State] 连接状态变为: ${pc?.connectionState}`);
  };
};

// --- Socket.IO 事件处理 ---
const setupSocketListeners = () => {
  socket.on('connect', async () => {
    console.log('[Socket] 成功连接到信令服务器');
    
    // 1. 创建 Peer Connection
    createPeerConnection();

    // 2. 创建并发送 Offer
    if (pc) {
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);
      console.log('[Offer] 创建并发送 Offer');
      socket.emit('offer', {
        sdp: pc.localDescription?.sdp,
      });
    }
  });

  // 处理来自服务器的 Answer
  socket.on('answer', async (answer) => {
    console.log('[Answer] 收到服务器的 Answer');
    if (pc && answer) {
      await pc.setRemoteDescription(new RTCSessionDescription(answer));
    }
  });

  socket.on('disconnect', () => {
    console.log('[Socket] 与服务器断开连接');
    disconnect();
  });
};

// --- 主要逻辑 ---

// 连接到服务器
const connect = () => {
  console.log('[Connect] 开始连接...');
  socket = io(SERVER_URL);
  setupSocketListeners();
  setUiControls(true);
};

// 断开连接
const disconnect = () => {
  console.log('[Disconnect] 断开连接...');
  
  // 关闭 Peer Connection
  if (pc) {
    pc.close();
    pc = null;
  }

  // 清理视频显示
  remoteVideo.srcObject = null;

  // 断开 socket 连接
  if (socket) {
    socket.disconnect();
  }

  setUiControls(false);
};

// --- 事件绑定 ---
connectBtn.addEventListener('click', connect);
disconnectBtn.addEventListener('click', disconnect);