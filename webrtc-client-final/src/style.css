:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.controls {
  margin-bottom: 2rem;
  display: flex;
  gap: 10px;
  justify-content: center;
  align-items: center;
}

input[type="text"] {
  padding: 0.5rem;
  font-size: 1rem;
  border-radius: 5px;
  border: 1px solid #555;
}

button {
  padding: 0.6rem 1rem;
  font-size: 1rem;
  border-radius: 8px;
  border: 1px solid transparent;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:disabled {
  background-color: #333;
  cursor: not-allowed;
  border-color: #444;
}


.videos {
  display: flex;
  gap: 2rem;
  justify-content: center;
  align-items: flex-start;
}

.video-container {
  background-color: #333;
  border-radius: 8px;
  padding: 1rem;
  width: 480px;
}

video {
  width: 100%;
  height: auto;
  background-color: black;
  border-radius: 5px;
}

h1 {
  font-size: 2.5em;
  line-height: 1.1;
  margin-bottom: 1.5rem;
}

h2 {
  margin-top: 0;
  font-size: 1.2em;
  color: #aaa;
}
