#!/usr/bin/env python3
"""
测试 aiortc 是否能正常导入和运行
"""

print("开始测试 aiortc...")

try:
    print("1. 测试基本导入...")
    import aiortc
    print(f"   ✓ aiortc 版本: {aiortc.__version__}")
    
    print("2. 测试 RTCPeerConnection 导入...")
    from aiortc import RTCPeerConnection, RTCSessionDescription
    print("   ✓ RTCPeerConnection 导入成功")
    
    print("3. 测试 MediaPlayer 导入...")
    from aiortc.contrib.media import MediaPlayer
    print("   ✓ MediaPlayer 导入成功")
    
    print("4. 测试创建 RTCPeerConnection...")
    pc = RTCPeerConnection()
    print("   ✓ RTCPeerConnection 创建成功")
    
    print("5. 测试 av 库...")
    import av
    print(f"   ✓ av 版本: {av.__version__}")
    
    print("6. 测试 opencv...")
    import cv2
    print(f"   ✓ opencv 版本: {cv2.__version__}")
    
    print("7. 测试摄像头检测...")
    import platform
    system = platform.system()
    print(f"   操作系统: {system}")
    
    if system == "Darwin":
        print("   测试 macOS 摄像头访问...")
        for i in range(3):
            try:
                cap = cv2.VideoCapture(i)
                if cap.isOpened():
                    print(f"   ✓ 找到摄像头设备索引: {i}")
                    cap.release()
                    break
                cap.release()
            except Exception as e:
                print(f"   设备 {i} 错误: {e}")
    
    print("\n所有测试通过！aiortc 应该可以正常工作。")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
except Exception as e:
    print(f"❌ 运行错误: {e}")
    import traceback
    traceback.print_exc()
