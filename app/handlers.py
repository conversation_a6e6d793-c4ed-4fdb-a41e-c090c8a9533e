from .server import sio
from .webrtc import handle_offer, cleanup_pc

@sio.event
async def connect(sid, environ):
    """
    处理新的客户端连接。
    """
    print(f'[连接成功] 客户端已连接: {sid}')

@sio.on('offer')
async def offer(sid, data):
    """
    处理来自客户端的 WebRTC Offer。
    """
    print(f'[Offer] 收到来自 {sid} 的 Offer')
    answer = await handle_offer(sid, data['sdp'])
    await sio.emit('answer', answer, to=sid)
    print(f'[Answer] 已向 {sid} 发送 Answer')

@sio.event
async def disconnect(sid):
    """
    处理客户端断开连接的事件。
    """
    print(f'[连接断开] 客户端已断开: {sid}')
    await cleanup_pc(sid)
