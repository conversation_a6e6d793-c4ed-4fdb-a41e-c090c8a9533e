import asyncio
import logging
import os
from typing import Dict, Set

from aiortc import RTCPeerConnection, RTCSessionDescription
from aiortc.contrib.media import MediaPlayer

# 获取项目根目录
ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

# --- 全局变量 ---
# 用一个集合来存储所有的 Peer Connection
pcs: Set[RTCPeerConnection] = set()
# 用一个字典来存储每个 sid 对应的 Peer Connection
pcs_by_sid: Dict[str, RTCPeerConnection] = {}

# --- 摄像头设备检测函数 ---
def find_camera_device():
    """查找可用的摄像头设备"""
    import glob
    import cv2
    
    # 检查 /dev/video* 设备
    video_devices = glob.glob('/dev/video*')
    print(f"发现视频设备: {video_devices}")
    
    for device in sorted(video_devices):
        try:
            # 先用 OpenCV 测试设备是否可用
            test_cap = cv2.VideoCapture(device)
            if test_cap.isOpened():
                # 尝试读取一帧来确保设备真正可用
                ret, frame = test_cap.read()
                test_cap.release()
                if ret and frame is not None:
                    print(f"找到可用摄像头: {device}")
                    return device
            test_cap.release()
        except Exception as e:
            print(f"设备 {device} 不可用: {e}")
            continue
    
    return None

# --- WebRTC 核心功能 ---
async def handle_offer(sid: str, offer_sdp: str):
    """
    处理来自客户端的 Offer。
    """
    offer = RTCSessionDescription(sdp=offer_sdp, type="offer")

    pc = RTCPeerConnection()
    pcs.add(pc)
    pcs_by_sid[sid] = pc

    @pc.on("connectionstatechange")
    async def on_connectionstatechange():
        print(f"客户端 {sid} 的连接状态变为: {pc.connectionState}")
        if pc.connectionState == "failed":
            await pc.close()
            pcs.discard(pc)
            if sid in pcs_by_sid:
                del pcs_by_sid[sid]

    # 查找可用的摄像头设备
    camera_device = find_camera_device()
    if not camera_device:
        raise Exception("未找到可用的摄像头设备")

    # 使用 MediaPlayer 直接读取摄像头
    try:
        print(f"尝试连接摄像头: {camera_device}")
        player = MediaPlayer(camera_device, format="v4l2")
        
        if player.video:
            pc.addTrack(player.video)
            print(f"成功添加摄像头视频轨道: {camera_device}")
        else:
            raise Exception("摄像头没有视频流")
            
    except Exception as e:
        print(f"连接摄像头失败: {e}")
        raise Exception(f"无法连接摄像头设备: {e}")

    await pc.setRemoteDescription(offer)
    answer = await pc.createAnswer()
    await pc.setLocalDescription(answer)

    return {"sdp": pc.localDescription.sdp, "type": "answer"}


async def cleanup_pc(sid: str):
    """
    清理指定客户端的 Peer Connection。
    """
    if sid in pcs_by_sid:
        pc = pcs_by_sid[sid]
        await pc.close()
        pcs.discard(pc)
        del pcs_by_sid[sid]
        print(f"已清理客户端 {sid} 的 Peer Connection")